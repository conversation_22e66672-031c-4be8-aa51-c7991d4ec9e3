package com.cosfo.mall.home;

import com.cosfo.mall.market.home.service.HomeService;
import com.cosfo.mall.market.model.query.MarketItemQuery;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/4/10 14:49
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class HomeServiceTest {

    @Resource
    private HomeService homeV2Service;

    @Test
    public void testUnlogin() {
//        MarketItemDTO marketItemDTO = new MarketItemDTO();
//        marketItemDTO.setClassificationId(676L);
//        marketItemDTO.setTenantId(1003L);
//        ResultDTO resultDTO = homeService.unLoginListAll(1, 10, marketItemDTO);
//        System.out.println(JSON.toJSONString(resultDTO));
    }

    @Test
    public void testlogin() {
        MarketItemQuery marketItemQuery = new MarketItemQuery();
        marketItemQuery.setClassificationId(1150L);
        marketItemQuery.setTenantId(2L);
        marketItemQuery.setStoreId(4260L);
        marketItemQuery.setPageNum(1);
        marketItemQuery.setPageSize(12);
        LoginContextInfoDTO requestContextInfoDTO = new LoginContextInfoDTO();
        requestContextInfoDTO.setTenantId(2L);
        requestContextInfoDTO.setStoreId(4260L);
        homeV2Service.listAll(1, 12, marketItemQuery, requestContextInfoDTO);
    }

    @Test
    public void testStock(){
//        308366655114
        LoginContextInfoDTO requestContextInfoDTO = new LoginContextInfoDTO();
        requestContextInfoDTO.setTenantId(2L);
        requestContextInfoDTO.setStoreId(4260L);
        homeV2Service.selectDetail(requestContextInfoDTO, 2210L);

    }
}
