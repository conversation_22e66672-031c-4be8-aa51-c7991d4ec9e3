package com.cosfo.mall.order.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.mall.bill.mapper.BillProfitSharingOrderMapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingSnapshotMapper;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.*;
import com.cosfo.mall.common.context.shard.ProfitSharingDeliveryTypeEnums;
import com.cosfo.mall.common.utils.AssertCheckDefault;
import com.cosfo.mall.order.mapper.OrderAgentSkuFeeRuleSnapshotMapper;
import com.cosfo.mall.order.model.po.OrderAgentSkuFeeRuleSnapshot;
import com.cosfo.mall.order.service.ProfitSharingCalculate;
import com.cosfo.mall.payment.mapper.PaymentCombinedDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderItemSnapshotQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_DOWN;
import static java.math.BigDecimal.ROUND_HALF_UP;
import static java.math.BigDecimal.ZERO;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Slf4j
@Service
public class ServiceFeeProfitSharingCalculate implements ProfitSharingCalculate {
    @Resource
    private BillProfitSharingService billProfitSharingService;
    @Resource
    private OrderAgentSkuFeeRuleSnapshotMapper orderAgentSkuFeeRuleSnapshotMapper;
    @Resource
    private PaymentService paymentService;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;
    @Resource
    private BillProfitSharingOrderMapper billProfitSharingOrderMapper;
    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @Resource
    private OrderItemSnapshotQueryProvider orderItemSnapshotQueryProvider;
    @Resource
    private RefundService refundService;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;

    /**
     * 2024.1.1之前四舍五入
     */
    private static final int V1_CAL_METHODS = ROUND_HALF_UP;
    /**
     * 2024.1.1之后向下取整
     */
    private static final int V2_CAL_METHODS = ROUND_DOWN;

    /**
     * 新规则调整时间
     */
    private static final LocalDateTime NEW_RULE_EFFECTIVE_TIME = LocalDateTime.of(2024, 1, 1, 0, 0, 0);

    @Override
    public boolean support(Integer deliveryType, Integer profitSharingRuleType, Integer type) {
        return ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode().equals(profitSharingRuleType);
    }

    /**
     * 计算本次分账的手续费
     * 1、如果满足 原支付金额 - 退款金额 - 历史确认金额 = 本次分账金额 则视为全部交易确认（都是支付单维度的），则本次分账手续费 = 交易总手续费 - 退款总手续费（需要每一笔退款手续费都向下取）- 历史确认手续费
     * 2、否则按照 本次分账金额 / 交易总金额 * 交易总手续费（向下取整）
     *
     * @param billProfitSharingSnapshotDtos
     * @param accountProfitSharingPriceMap
     */
    @Override
    public void profitSharingCalculate(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, Map<Long, BigDecimal> accountProfitSharingPriceMap) {
        // 查询是否是全部交易确认
        BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO = billProfitSharingSnapshotDtos.get(0);
        Long orderId = billProfitSharingSnapshotDTO.getOrderId();
        Long tenantId = billProfitSharingSnapshotDTO.getTenantId();
        String profitSharingNo = billProfitSharingSnapshotDTO.getProfitSharingNo();
        // 总交易金额
        PaymentDTO paymentDTO = paymentService.querySuccessPaymentInfoByOrderId(orderId, tenantId);
        BigDecimal paymentTotalPrice = paymentDTO.getTotalPrice();
        // 如果是组合支付，总交易金额取汇付支付单的支付金额
        if (Objects.equals(paymentDTO.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            List<PaymentCombinedDetail> details = paymentCombinedDetailService.selectByCombinedPaymentNo(paymentDTO.getPaymentNo());
            paymentTotalPrice = details.stream()
                    .filter(el -> Objects.equals(el.getOnlinePayChannel(), OnlinePayChannelEnum.HUIFU_PAY.getChannel()))
                    .map(PaymentCombinedDetail::getTotalPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // 如果用则直接使用 没有则计算
        log.info("手续费计算流程开始，金额 = {}元，费率 = {}元，手续费 = {}元", paymentTotalPrice, paymentDTO.getFeeRate(), paymentDTO.getFeeAmount());
        BigDecimal feeRate = paymentDTO.getFeeRate();
        BigDecimal totalServiceCharge = Optional.ofNullable(paymentDTO.getFeeAmount()).orElse(NumberUtil.mul(paymentTotalPrice, NumberUtil.div(feeRate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP));
        List<PaymentItem> paymentItemList = paymentDTO.getPaymentItemList();
        List<Long> orderIds = paymentItemList.stream().map(PaymentItem::getOrderId).collect(Collectors.toList());

        // 历史售后金额
        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setTenantId(tenantId);
        req.setOrderIds(orderIds);
        req.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        List<OrderAfterSaleResp> orderAfterSaleDTOS = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(req));
        // 如果是组合支付，过滤掉余额、非现金支付售后
        if (Objects.equals(paymentDTO.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            orderAfterSaleDTOS = orderAfterSaleDTOS.stream()
                    .filter(el -> !OrderAfterSaleServiceTypeEnum.getNativeCombinedServiceType().contains(el.getServiceType()))
                    .collect(Collectors.toList());
        }
        List<Long> orderAfterSaleIds = orderAfterSaleDTOS.stream().map(OrderAfterSaleResp::getId).collect(Collectors.toList());
        List<Refund> refunds = CollectionUtils.isEmpty(orderAfterSaleDTOS) ? Collections.emptyList() : refundService.queryAfterSaleIds(tenantId, orderAfterSaleIds);
        BigDecimal refundTotalPrice = orderAfterSaleDTOS.stream().map(OrderAfterSaleResp::getTotalPrice).reduce(ZERO, BigDecimal::add);
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryByTenantIdAndOrderIds(tenantId, orderIds);
        // 设置分账类型
        setDeliveryType(billProfitSharingSnapshots);
        BigDecimal totalProfitSharingPrice = getTotalProfitSharingPrice(billProfitSharingSnapshots, tenantId, orderIds);
        // 本次分账金额
        BigDecimal thisOneProfitSharingPrice = getThisOneProfitSharingPrice(profitSharingNo, billProfitSharingSnapshots);

        BigDecimal thisOneServiceCharge;
        // 总分账金额 = 总支付金额 - 总退款金额则视为本次为最后一次
        if (totalProfitSharingPrice.compareTo(paymentTotalPrice.subtract(refundTotalPrice)) == 0) {
            BigDecimal totalRefundServiceCharge = getRefundServiceCharge(refunds, paymentTotalPrice, totalServiceCharge);
            BigDecimal historyServiceCharge = getHistoryServiceCharge(profitSharingNo, billProfitSharingSnapshots);
            thisOneServiceCharge = NumberUtil.sub(totalServiceCharge, historyServiceCharge, totalRefundServiceCharge);
        } else {
            // 否则按照 本次分账金额 / 交易总金额 * 交易总手续费（向下取整）
            thisOneServiceCharge = NumberUtil.mul(totalServiceCharge, NumberUtil.div(thisOneProfitSharingPrice, paymentTotalPrice)).setScale(NumberConstant.TWO, RoundingMode.DOWN);
        }

        // 本次手续费为0
        if (thisOneProfitSharingPrice.compareTo(ZERO) == 0 || thisOneServiceCharge.compareTo(ZERO) == 0) {
            billProfitSharingSnapshotDtos.forEach(el -> {
                el.setOriginPrice(ZERO);
                el.setProfitSharingPrice(ZERO);
                billProfitSharingService.updateBillProfitSharingSnapshot(el);
            });
            return;
        }
        // 订单手续费
        if (ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode().equals(billProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getMappingType())) {
            calculateByRate(billProfitSharingSnapshotDtos, thisOneServiceCharge);
        } else if (ProfitSharingRuleMappingTypeEnum.AVERAGE_RATIO.getCode().equals(billProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getMappingType())) {
            calculateByAverage(billProfitSharingSnapshotDtos, thisOneServiceCharge, thisOneProfitSharingPrice, accountProfitSharingPriceMap);
        }
    }

    private void setDeliveryType(List<BillProfitSharingSnapshot> billProfitSharingSnapshots) {
        List<Long> orderIds = billProfitSharingSnapshots.stream().map(BillProfitSharingSnapshot::getOrderId).collect(Collectors.toList());
        List<OrderResp> orderInfoList = RpcResultUtil.handle(orderQueryProvider.queryByIds(orderIds));
        Map<Long, OrderResp> orderInfoMap = orderInfoList.stream().collect(Collectors.toMap(OrderResp::getId, el -> el));
        billProfitSharingSnapshots.stream().filter(el -> Objects.isNull(el.getDeliveryType())).collect(Collectors.toList()).forEach(el -> {
            OrderResp order = orderInfoMap.get(el.getOrderId());
            if (Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode())) {
                el.setDeliveryType(ProfitSharingDeliveryTypeEnums.THIRD_DELIVERY.getType());
            }
        });
    }

    private BigDecimal getHistoryServiceCharge(String profitSharingNo, List<BillProfitSharingSnapshot> billProfitSharingSnapshots) {
        return billProfitSharingSnapshots.stream()
                .filter(el -> Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .filter(el -> Objects.nonNull(el.getProfitSharingPrice()))
                .filter(el -> !Objects.equals(profitSharingNo, el.getProfitSharingNo()))
                .map(BillProfitSharingSnapshot::getProfitSharingPrice)
                .reduce(ZERO, BigDecimal::add)
                .negate();
    }

    /**
     * 计算总退款返还金额
     *
     * @param refunds    退款集合列表
     * @param paymentTotalPrice  支付金额
     * @param totalServiceCharge 总手续费
     * @return
     */
    private BigDecimal getRefundServiceCharge(List<Refund> refunds, BigDecimal paymentTotalPrice, BigDecimal totalServiceCharge) {
        // 2024.1.1之前四舍五入 后续向下取整
        BigDecimal totalRefundServiceCharge = BigDecimal.ZERO;
        for (Refund refund : refunds) {
            BigDecimal refundPrice = refund.getRefundPrice();
            LocalDateTime successTime = refund.getSuccessTime();
            int roundMode = Objects.nonNull(successTime) && successTime.isBefore(NEW_RULE_EFFECTIVE_TIME) ? V1_CAL_METHODS : V2_CAL_METHODS;
            BigDecimal refundServiceCharge = NumberUtil.mul(totalServiceCharge, NumberUtil.div(refundPrice, paymentTotalPrice)).setScale(NumberConstant.TWO, roundMode);
            totalRefundServiceCharge = totalRefundServiceCharge.add(refundServiceCharge);
        }
        return totalRefundServiceCharge;
    }

    private BigDecimal getTotalProfitSharingPrice(List<BillProfitSharingSnapshot> billProfitSharingSnapshots, Long tenantId, List<Long> orderIds) {
        BigDecimal totalProfitSharingPrice = billProfitSharingSnapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .filter(el -> Objects.nonNull(el.getProfitSharingPrice()))
                .map(BillProfitSharingSnapshot::getProfitSharingPrice)
                .reduce(ZERO, BigDecimal::add);

        // 分账给个人就已经包含代仓了
        boolean personalFlag = billProfitSharingSnapshots.stream().filter(el -> Objects.equals(el.getDeliveryType(), ProfitSharingDeliveryTypeEnums.THIRD_DELIVERY.getType())).anyMatch(el -> Objects.equals(el.getType(), BillProfitSharingSnapshotTypeEnum.ALL.getCode()));
        if (personalFlag) {
            return totalProfitSharingPrice;
        }
        List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots = orderAgentSkuFeeRuleSnapshotMapper.queryByTenantIdAndOrderIds(tenantId, orderIds);
        BigDecimal agentWarehousePrice = orderAgentSkuFeeRuleSnapshots.stream()
                .filter(el -> Objects.nonNull(el.getPrice()))
                .map(OrderAgentSkuFeeRuleSnapshot::getPrice)
                .reduce(ZERO, BigDecimal::add);
        return NumberUtil.add(totalProfitSharingPrice, agentWarehousePrice);
    }

    private BigDecimal getThisOneProfitSharingPrice(String profitSharingNo, List<BillProfitSharingSnapshot> billProfitSharingSnapshots) {
        billProfitSharingSnapshots = billProfitSharingSnapshots.stream().filter(el -> Objects.equals(el.getProfitSharingNo(), profitSharingNo)).collect(Collectors.toList());
        BigDecimal thisOneProfitSharingPrice = billProfitSharingSnapshots.stream()
                .filter(el -> !Objects.equals(el.getProfitSharingType(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode()))
                .filter(el -> Objects.nonNull(el.getProfitSharingPrice()))
                .map(BillProfitSharingSnapshot::getProfitSharingPrice)
                .reduce(ZERO, BigDecimal::add);

        // 分账给个人就已经包含代仓了
        boolean personalFlag = billProfitSharingSnapshots.stream().filter(el -> Objects.equals(el.getDeliveryType(), ProfitSharingDeliveryTypeEnums.THIRD_DELIVERY.getType())).anyMatch(el -> Objects.equals(el.getType(), BillProfitSharingSnapshotTypeEnum.ALL.getCode()));
        if (personalFlag) {
            return thisOneProfitSharingPrice;
        }

        Long tenantId = billProfitSharingSnapshots.get(0).getTenantId();
        Long orderId = billProfitSharingSnapshots.get(0).getOrderId();
        List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots = orderAgentSkuFeeRuleSnapshotMapper.queryByTenantIdAndOrderId(tenantId, orderId);
        BigDecimal thisOneAgentWarehousePrice = orderAgentSkuFeeRuleSnapshots.stream()
                .filter(el -> Objects.nonNull(el.getPrice()))
                .map(OrderAgentSkuFeeRuleSnapshot::getPrice)
                .reduce(ZERO, BigDecimal::add);
        return NumberUtil.add(thisOneProfitSharingPrice, thisOneAgentWarehousePrice);
    }

    /**
     * 按比例计算
     *
     * @param billProfitSharingSnapshotDtos
     * @param serviceFee
     */
    private void calculateByRate(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, BigDecimal serviceFee) {
        // 计算税费
        BigDecimal residuePrice = serviceFee;
        BillProfitSharingSnapshotDTO barndBillProfitSharingSnapShotDto = null;
        for (BillProfitSharingSnapshotDTO billProfitSharingSnapshotDto : billProfitSharingSnapshotDtos) {
            // 品牌方手续费最后一个计算
            if (!billProfitSharingSnapshotDto.getTenantId().equals(billProfitSharingSnapshotDto.getAccountId())) {
                billProfitSharingSnapshotDto.setOriginPrice(serviceFee.negate());
                BigDecimal profitSharingPrice = NumberUtil.mul(serviceFee, NumberUtil.div(billProfitSharingSnapshotDto.getNumber(), NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
                residuePrice = NumberUtil.sub(residuePrice, profitSharingPrice);
                billProfitSharingSnapshotDto.setProfitSharingPrice(profitSharingPrice.negate());
                billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDto);
            } else {
                barndBillProfitSharingSnapShotDto = billProfitSharingSnapshotDto;
            }
        }

        AssertCheckDefault.expectNotNull(barndBillProfitSharingSnapShotDto, "帆台分账明细不存在:" + JSON.toJSONString(billProfitSharingSnapshotDtos));
        // 帆台
        barndBillProfitSharingSnapShotDto.setOriginPrice(serviceFee.negate());
        barndBillProfitSharingSnapShotDto.setProfitSharingPrice(residuePrice.negate());
        billProfitSharingService.updateBillProfitSharingSnapshot(barndBillProfitSharingSnapShotDto);
    }

    /**
     * 按分账比例均摊
     *
     * @param billProfitSharingSnapshotDtos
     * @param serviceFee
     * @param totalPrice
     */
    private void calculateByAverage(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, BigDecimal serviceFee, BigDecimal totalPrice, Map<Long, BigDecimal> accountProfitSharingPriceMap) {
        // 计算手续费
        BigDecimal residuePrice = serviceFee;
        BillProfitSharingSnapshotDTO brandBillProfitSharingSnapShotDto = null;
        for (BillProfitSharingSnapshotDTO billProfitSharingSnapshotDto : billProfitSharingSnapshotDtos) {
            // 品牌方手续费最后一个计算
            if (!billProfitSharingSnapshotDto.getTenantId().equals(billProfitSharingSnapshotDto.getAccountId())) {
                billProfitSharingSnapshotDto.setOriginPrice(serviceFee.negate());
                BigDecimal price = accountProfitSharingPriceMap.get(billProfitSharingSnapshotDto.getAccountId());
                // 计算比例
                BigDecimal rate = NumberUtil.div(price, totalPrice);
                BigDecimal profitSharingPrice = BigDecimal.ZERO;
                profitSharingPrice = NumberUtil.mul(serviceFee, rate).setScale(NumberConstant.TWO, ROUND_HALF_UP);
                if (price.compareTo(totalPrice) > NumberConstant.ZERO) {
                    profitSharingPrice = serviceFee;
                }

                if (price.compareTo(BigDecimal.ZERO) < NumberConstant.ZERO) {
                    profitSharingPrice = BigDecimal.ZERO;
                }

                residuePrice = NumberUtil.sub(residuePrice, profitSharingPrice);
                billProfitSharingSnapshotDto.setProfitSharingPrice(profitSharingPrice.negate());
                billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDto);
            } else {
                brandBillProfitSharingSnapShotDto = billProfitSharingSnapshotDto;
            }
        }

//        AssertCheckDefault.expectNotNull(brandBillProfitSharingSnapShotDto, "帆台分账明细不存在:" + JSON.toJSONString(billProfitSharingSnapshotDtos));
        // 帆台
        brandBillProfitSharingSnapShotDto.setOriginPrice(serviceFee.negate());
        brandBillProfitSharingSnapShotDto.setProfitSharingPrice(residuePrice.negate());
        billProfitSharingService.updateBillProfitSharingSnapshot(brandBillProfitSharingSnapShotDto);
    }

    /**
     * 获取平台手续费
     *
     * @param billProfitSharingSnapshotDtos
     * @Param orderTotalPrice 当前订单要分账总金额
     * @return
     */
//    private BigDecimal getServiceFee(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, BigDecimal orderTotalPrice){
//        Long orderId = billProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getOrderId();
//        // 查询订单信息
////        Order order = orderMapper.selectByPrimaryKey(orderId);
//        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(orderId));
//
//        Long tenantId= orderDTO.getTenantId();
//        // 查询支付单
//        PaymentDTO paymentDTO = paymentService.querySuccessByOrderId(orderDTO.getTenantId(), orderDTO.getId());
//        // 根据支付单查询全部订单项
//        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentId(paymentDTO.getId());
//
//        // 手续费率
//        BigDecimal fee_rate = WechatPaymentConstant.TAX_RATE;
//        if(OnlinePayChannelEnum.HUIFU_PAY.getChannel().equals(orderDTO.getOnlinePayChannel())) {
//            fee_rate = Objects.nonNull(paymentDTO.getFeeRate()) ? paymentDTO.getFeeRate() : WechatPaymentConstant.TAX_RATE;
//        }
//
//        // 判断是否多个订单一起支付
//        if(paymentItems.size() > NumberConstant.ONE){
//            // 获取其他订单Id
//            List<Long> otherOrderIds = paymentItems.stream().map(PaymentItem::getOrderId).filter(id -> !id.equals(orderDTO.getId())).collect(Collectors.toList());
//            List<BillProfitSharingOrderDTO> billProfitSharingOrderDTOS = billProfitSharingOrderService.queryByOrderIds(otherOrderIds, orderDTO.getTenantId());
//            // 判断其他订单是否分账都成功
//            boolean allMatch = billProfitSharingOrderDTOS.stream().allMatch(billProfitSharingOrderDTO -> ProfitSharingOrderStatusEnum.FINISHED.getCode().equals(billProfitSharingOrderDTO.getStatus()));
//            if(allMatch){
//                /**
//                 * 计算要分账总金额
//                 */
//                List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryByTenantIdAndOrderIds(tenantId, otherOrderIds);
//                // 其他订单要分账总金额
//                List<BillProfitSharingSnapshot> profitSharingSnapshots = billProfitSharingSnapshots.stream().
//                        filter(billProfitSharingSnapshot -> !ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode().equals(billProfitSharingSnapshot.getProfitSharingType())).collect(Collectors.toList());
//                BigDecimal totalPrice = profitSharingSnapshots.stream().map(profitSharingSnapshot -> Objects.nonNull(profitSharingSnapshot.getProfitSharingPrice()) ? profitSharingSnapshot.getProfitSharingPrice() : ZERO).reduce(ZERO, BigDecimal::add);
//
//                // 代仓品费用
//                List<OrderAgentSkuFeeRuleSnapshot> orderAgentSkuFeeRuleSnapshots = orderAgentSkuFeeRuleSnapshotMapper.queryByTenantIdAndOrderIds(tenantId, otherOrderIds);
//                if(!CollectionUtils.isEmpty(orderAgentSkuFeeRuleSnapshots)){
//                    BigDecimal agentItemTotalPrice = orderAgentSkuFeeRuleSnapshots.stream()
//                            .map(OrderAgentSkuFeeRuleSnapshot -> Objects.nonNull(OrderAgentSkuFeeRuleSnapshot.getPrice()) ? OrderAgentSkuFeeRuleSnapshot.getPrice() : ZERO)
//                            .reduce(ZERO, BigDecimal::add);
//                    totalPrice = NumberUtil.add(totalPrice , agentItemTotalPrice);
//                }
//
//                // 其他订单手续费
//                List<BillProfitSharingSnapshot> serviceFeeProfitSharingSnapshots = billProfitSharingSnapshots.stream().
//                        filter(billProfitSharingSnapshot -> ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode().equals(billProfitSharingSnapshot.getProfitSharingType())).collect(Collectors.toList());
//                BigDecimal otherServiceFee = serviceFeeProfitSharingSnapshots.stream().map(profitSharingSnapshot -> Objects.nonNull(profitSharingSnapshot.getProfitSharingPrice()) ? profitSharingSnapshot.getProfitSharingPrice() : ZERO).reduce(ZERO, BigDecimal::add);
//
//                // 所有订单分账总金额
//                totalPrice = NumberUtil.add(totalPrice, orderTotalPrice);
//                // 计算总手续费
//                BigDecimal serviceFee = NumberUtil.mul(totalPrice, NumberUtil.div(fee_rate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
//                // 扣除其他订单手续费
//                serviceFee = NumberUtil.sub(serviceFee, otherServiceFee.negate());
//                return serviceFee;
//            }
//        }
//
//        // 如果不是最后一个要分账的订单，单独计算手续费
//        BigDecimal serviceFee = NumberUtil.mul(orderTotalPrice, NumberUtil.div(fee_rate, NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
//        return serviceFee;
//    }
}