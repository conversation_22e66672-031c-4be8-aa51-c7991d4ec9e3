package com.cosfo.mall.order.controller;

import com.cosfo.mall.openapi.service.OrderNotifyBizService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/2/29 下午3:58
 */
@RestController
@RequestMapping("/order/notify")
@Slf4j
public class OrderNotifyController {

    @Resource
    private OrderNotifyBizService orderNotifyBizService;


    @RequestMapping(value = "/delivering", method = RequestMethod.POST)
    public CommonResult<List<Long>> notifyOrderDelivering(@RequestBody List<Long> orderIds){
        List<Long> failOrderIdList = new ArrayList<>();
        for (Long orderId : orderIds) {
            try {
                orderNotifyBizService.notifyOrderDelivering(orderId);
            } catch (Exception e) {
                log.error("开放平台订单发货回传异常， orderId={}", orderId, e);
                failOrderIdList.add(orderId);
            }
        }

        return CommonResult.ok(failOrderIdList);
    }

}
