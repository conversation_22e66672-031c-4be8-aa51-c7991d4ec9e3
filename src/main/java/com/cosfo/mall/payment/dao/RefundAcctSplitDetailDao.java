package com.cosfo.mall.payment.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.mall.payment.model.po.RefundAcctSplitDetail;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/19
 */
public interface RefundAcctSplitDetailDao extends IService<RefundAcctSplitDetail> {

    /**
     * 查询退款分账明细
     *
     * @param refundId
     * @param tenantId
     * @return
     */
    List<RefundAcctSplitDetail> queryByRefundId(Long refundId, Long tenantId);

    /**
     * 批量保存
     *
     * @param refundAcctSplitDetails
     */
    void saveBatch(List<RefundAcctSplitDetail> refundAcctSplitDetails);
}
