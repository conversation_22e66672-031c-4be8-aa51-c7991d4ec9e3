package com.cosfo.mall.payment.mapper;

import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * @description: ${description}
 * @author: George
 * @date: 2025-04-25
 **/
@Mapper
public interface PaymentCombinedOrderDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PaymentCombinedOrderDetail record);

    int insertSelective(PaymentCombinedOrderDetail record);

    PaymentCombinedOrderDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PaymentCombinedOrderDetail record);

    int updateByPrimaryKey(PaymentCombinedOrderDetail record);

    void batchInsert(List<PaymentCombinedOrderDetail> list);

    /**
     * 根据组合明细ID查询
     *
     * @param combinedDetailIds
     * @return
     */
    List<PaymentCombinedOrderDetail> selectByCombinedDetailIds(@Param("tenantId") Long tenantId, @Param("ids") Collection<Long> combinedDetailIds);
}