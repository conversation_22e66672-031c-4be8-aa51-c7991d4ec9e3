package com.cosfo.mall.payment.service;

import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/19
 */
public interface RefundAcctSplitDetailService {

    /**
     * 保存
     *
     * @param refundAcctSplitDetailDTOList
     * @param refundId
     */
    void save(List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOList, Long refundId);

    /**
     * 查询逆向退款明细
     *
     * @param refundId
     * @param tenantId
     * @return
     */
    List<RefundAcctSplitDetailDTO> queryByRefundId(Long refundId, Long tenantId);
}
