package com.cosfo.mall.payment.service;

import com.cosfo.mall.order.model.po.HuiFuPayment;

/**
 * @Author: fansongsong
 * @Date: 2023-11-17
 * @Description:
 */
public interface HuifuPaymentService {

    /**
     * 根据hfSeqId更新汇付支付单
     *
     * @param huiFuPayment
     * @return
     */
    int updateByHfSeqId(HuiFuPayment huiFuPayment,String hfSeqId);

    /**
     * 根据支付单ID更新汇付支付单
     *
     * @param huiFuPayment
     * @param paymentId
     * @return
     */
    int updateByPaymentId(HuiFuPayment huiFuPayment, Long paymentId);

    /**
     * 根据交易单号+汇付Id查询汇付支付单
     * @param reqSeqId
     * @param huifuId
     * @return
     */
    HuiFuPayment selectByReqSeqId(String reqSeqId, String huifuId);

    /**
     * 根据支付单ID查询汇付支付单
     *
     * @param paymentId
     * @return
     */
    HuiFuPayment selectByPaymentId(Long paymentId);

}
