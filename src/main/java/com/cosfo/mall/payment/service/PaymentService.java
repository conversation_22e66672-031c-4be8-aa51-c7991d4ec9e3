package com.cosfo.mall.payment.service;

import cn.hutool.http.server.HttpServerRequest;
import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharing;
import com.cosfo.mall.common.constants.ProfitSharingResultEnum;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.order.model.dto.HuiFuConfirmResponseDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.model.po.HuiFuiPaymentReceive;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.wechat.bean.notify.DirectNotify;
import com.cosfo.mall.wechat.bean.notify.NotifyResponse;
import com.cosfo.mall.wechat.bean.paymch.DirectQueryResult;
import com.cosfo.mall.wechat.bean.profitsharing.ProfitSharingOrderResult;
import com.cosfo.mall.wechat.bean.profitsharing.QueryOrderParams;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinNotifyDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinPayNotifyDTO;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/5/23  11:01
 */
public interface PaymentService {

    /**
     * 处理支付请求
     *
     * @param paymentRequest 请求对象
     * @return 请求结果
     */
    PaymentResult pay(PaymentRequest paymentRequest);

    /**
     * 根据商户订单号查询支付金额
     *
     * @param partyOrderId
     * @return
     */
    String queryByPartyOrderId(String partyOrderId);

    /**
     * 查询支付结果
     *
     * @param paymentId 支付单id
     * @return 支付结果
     */
    DirectQueryResult queryPayResult(Long paymentId);

    /**
     * 微信直连支付回调
     *
     * @param directNotify 回调信息
     * @param request      回调请求
     * @return 回调处理结果
     */
    NotifyResponse wxDirectPayNotify(DirectNotify directNotify, HttpServerRequest request);

    /**
     * 查询处理分账结果
     *
     * @param queryOrderParams
     * @param  billProfitSharingList
     * @return 分账结果
     */
    ResultDTO<ProfitSharingOrderResult> handleProfitSharingOrderResult(QueryOrderParams queryOrderParams, List<BillProfitSharing> billProfitSharingList);

    /**
     * 查询处理汇付分账结果
     *
     * @param queryOrderParams
     * @param billProfitSharingList
     * @return
     */
    ResultDTO<HuiFuConfirmResponseDTO> handleHuiFuProfitSharingOrderResult(QueryOrderParams queryOrderParams, List<BillProfitSharing> billProfitSharingList, BillProfitSharingOrderDTO billProfitSharingOrderDTO);

    /**
     * 查询支付信息
     *
     * @param orderId
     * @param tenantId
     * @return
     */
    PaymentDTO querySuccessPaymentInfoByOrderId(Long orderId, Long tenantId);

    /**
     * 汇付分账
     *
     * @param billProfitSharings 分账项
     * @Param unfreezeUnsplit 是否解冻
     */
    void huifuProfitSharing(List<BillProfitSharing> billProfitSharings, HuiFuPayment huiFuPayment, BillProfitSharingOrderDTO billProfitSharingOrderDTO);

    /**
     * 智付分账
     *
     * @param billProfitSharings        分账项
     * @param paymentDTO                支付信息
     * @param billProfitSharingOrderDTO 分账订单
     */
    void dinPayProfitSharing(List<BillProfitSharing> billProfitSharings, PaymentDTO paymentDTO, BillProfitSharingOrderDTO billProfitSharingOrderDTO);

    /**
     * 查询并更新智付分账状态
     *
     * @param billProfitSharings        分账项
     * @param paymentDTO                支付信息
     * @param billProfitSharingOrderDTO 分账订单
     */
    void queryAndUpdateDinPayProfitSharingStatus(List<BillProfitSharing> billProfitSharings, PaymentDTO paymentDTO, BillProfitSharingOrderDTO billProfitSharingOrderDTO);

    /**
     * 查询分账结果
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    ResultDTO queryProfitSharingResult(Long tenantId, Long orderId);

    /**
     * 汇付回调信息处理
     *
     */
    String huiFuPayNotification(HttpServletRequest request);

    /**
     * 查詢支付記錄
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    PaymentDTO querySuccessByOrderId(Long tenantId, Long orderId);

    /**
     * 汇付支付成功结果处理
     *
     * @param huiFuPaymentReceive
     * @param paymentId
     */
    void huifuPaySuccess(HuiFuiPaymentReceive huiFuPaymentReceive, Long paymentId);

    /**
     * 智付支付回调处理
     *
     * @param dinNotifyDTO
     */
    boolean dinNotifyPaySuccess(DinPayNotifyDTO dinNotifyDTO);

    /**
     * 事务内方法
     * @param huiFuPaymentReceive
     * @param payment
     * @param itemList
     * @param feeAmountNull
     * @param feeRateFuture
     */
    boolean huifuPaySuccessInTransaction(HuiFuiPaymentReceive huiFuPaymentReceive, Payment payment, List<PaymentItem> itemList, boolean feeAmountNull, CompletableFuture<String> feeRateFuture);


    /**
     * 发送分账结果消息
     *
     * @param orderId
     * @param resultEnum
     * @param total
     * @param successCnt
     */
    void profitSharingResultNotify(Long orderId, ProfitSharingResultEnum resultEnum, Integer total, Integer successCnt, String message);

    /**
     * 处理微信支付结果
     *
     * @param result
     */
    boolean handleWXPayNotifyTransaction(DirectQueryResult result);

    /**
     * 处理微信支付结果
     *
     * @param result
     */
    void handleWXPayNotify(DirectQueryResult result);


    /**
     * 查询订单支付费用
     *
     * @param tenantId 承租者id
     * @param orderId  订单id
     * @return {@link BigDecimal}
     */
    BigDecimal queryPaymentRateByOrderId(Long tenantId, Long orderId);

    /**
     * 查询外部支付单的状态
     *
     * @param payment
     * @return
     */
    PaymentResult queryExternalPaymentStatus(Payment payment);

    /**
     * 关闭支付单
     *
     * @param payment
     * @return
     */
    boolean closePaymentOrder(Payment payment);

    /**
     * 关闭外部支付单
     *
     * @param payment
     * @return
     */
    boolean closeExternalPaymentOrder(Payment payment);

    /**
     * 根据
     *
     * @param id
     * @return
     */
    Payment queryById(Long id);

    List<Payment> querySuccessPayments(Collection<Long> tenantIds, String startTime, String endTime);

    Payment queryPayment(Long orderId, Long tenantId);

    /**
     * 查询支付单根据退款单
     *
     * @param refundId
     * @return
     */
    Payment queryByRefundId(Long refundId);

    /**
     * 根据支付单编号查询
     *
     * @param paymentNo
     * @return
     */
    Payment queryByNo(String paymentNo);
}
