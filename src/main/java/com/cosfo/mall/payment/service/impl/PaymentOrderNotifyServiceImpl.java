package com.cosfo.mall.payment.service.impl;

import com.cosfo.mall.common.context.PayNotifyBizType;
import com.cosfo.mall.payment.model.bo.PayNotifyBO;
import com.cosfo.mall.payment.service.AbstractPaymentNotifyService;
import com.cosfo.mall.payment.service.PaymentOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * @description: 支付回调通知订单相关处理接口
 * @author: George
 * @date: 2024-08-19
 **/
@Service
@Slf4j
public class PaymentOrderNotifyServiceImpl extends AbstractPaymentNotifyService {

    @Resource
    private PaymentOrderService paymentOrderService;

    @Override
    protected String getBizType() {
        return PayNotifyBizType.ORDER.getBizType();
    }

    @Override
    protected void doBizNotify(PayNotifyBO payNotifyBO) {
        // 更新订单状态
        paymentOrderService.orderPaySuccess(payNotifyBO.getOrderIds());
    }
}
