package com.cosfo.mall.payment.model.request;

import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.security.PrivateKey;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 支付请求对象
 * @author: George
 * @date: 2023-08-29
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentRequest {

    /**
     * 订单编号
     */
    private List<String> orderNos;
    /**
     * 支付类型 1、微信 2、账期 3、余额 4、支付宝 5、0元 6、线下支付 7、非现金支付 8、组合支付
     */
    private Integer payType;
    /**
     * 支付类型 1、微信 2、账期 3、余额 4、支付宝 5、0元 6、线下支付 7、非现金支付 8、组合支付
     */
    private List<Integer> payTypes;

    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;
    /**
     * 是否H5请求
     */
    @Builder.Default
    private Boolean H5Request = false;

    //-------以上是前端传入的参数--------

    /**
     * 组合请求
     */
    private boolean combineRequest;

    /**
     * 组合支付详情
     */
    private List<PaymentRequest> combinePaymentRequest;


    /**
     * 支付单id
     */
    private Long paymentId;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 支付创建时间
     */
    private LocalDateTime paymentCreateTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店账号id
     */
    private Long accountId;

    /**
     * 本次支付的订单集合
     */
    private List<OrderResp> orders;

    /**
     * 支付项
     */
    private List<PaymentItem> paymentItemList;

    /**
     * 是否是組合包
     */
    private Boolean combine;

    /**
     * 用于汇付或者微信支付的支付单据的description，参考：
     * <a href="https://paas.huifu.com/partners/api/#/smzf/api_jhzs">...</a>
     * <a href="https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_1.shtml">...</a>
     */
    private String paymentDesc;

    /**
     * 交易类型
     *
     * @see com.cosfo.mall.common.constants.TradeTypeEnum
     */
    private String tradeType;

    /**
     * 商户/服务商appid（微信所需）
     */
    private String spAppid;

    /**
     * 商户/服务商id（微信所需）
     */
    private String spMchid;

    /**
     * 私钥（微信所需）
     */
    private PrivateKey wechatPrivateKey;

    /**
     * 微信证书地址
     */
    private String payCertPath;

    /**
     * 交易金额
     */
    private BigDecimal transAmt;

    /**
     * 支付标记 1：小程序汇付插件支付
     */
    private Integer payMark;

    /**
     * 线下 支付凭证
     */
    private String paymentReceipt;

    /**
     * 组合支付单id
     */
    private Long masterPaymentId;

    /**
     * 中央服务管理的channelID
     */
    private Long paymentChannelId;

}
