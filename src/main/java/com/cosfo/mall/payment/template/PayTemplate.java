package com.cosfo.mall.payment.template;

import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.constants.RedisKeyEnum;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.ThreadTokenHolder;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.vo.MerchantStoreAccountVO;
import com.cosfo.mall.merchant.service.MerchantStoreAccountService;
import com.cosfo.mall.order.mapper.OrderMapper;
import com.cosfo.mall.order.model.po.OrderItemSnapshot;
import com.cosfo.mall.order.service.OrderItemSnapshotService;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.dto.OrderPayResultDTO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.mall.wechat.api.HuiFuApi;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.CombineOrderQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 支付方法模版
 * @author: George
 * @date: 2023-08-29
 **/
@Slf4j
public abstract class PayTemplate {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private TenantService tenantService;
    @Resource
    private OrderItemSnapshotService orderItemSnapshotService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private CombineOrderQueryProvider combineOrderQueryProvider;
    @Resource
    private OrderService orderService;

    /**
     * 支付单商品名字描述的长度限制：127；huiFu & Wechat
     */
    private static final int DESC_SIZE_LIMIT = 127;


    /**
     * 支付方法
     *
     * @param request 支付请求对象
     * @return 支付结果
     */
    public PaymentResult pay(PaymentRequest request) {
        // 1、预处理 一些参数校验
        preProcess(request);
        // 2、组装支付参数
        assemblyPaymentInfo(request);
        // 3、加锁
        RLock lock = lock();

        PaymentResult result;
        try {
            // 4、校验上次支付单是否可用
            verifyLastPaymentStatus(request);

            // 5、更改订单的支付方式
            updateOrderPayType(request);

            // 6、创建支付单
            Long paymentId = createPaymentOrder(request);
            request.setPaymentId(paymentId);

            // 7、调用支付接口
            result = processPay(request);

            // 8、根据result做成功或者失败操作
            if (result.isSuccess()) {
                onSuccess(request, result);
            } else {
                onFailure(request, result);
            }

            //9、扩展操作
            postProcess(request, result);

            //10、返回支付信息
            return assemblyPaymentResult(request, result);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 更改订单的支付方式
     *
     * @param request
     */
    private void updateOrderPayType(PaymentRequest request) {
        List<OrderResp> orders = request.getOrders();
        orders.forEach(el -> {
            orderService.updatePayType(el.getId(), request.getPayType(), request.getOnlinePayChannel());
        });
    }

    /**
     * 组装支付结果
     *
     * @param request 支付请求对象
     * @param result  支付结果对象
     * @return 支付结果对象
     */
    protected PaymentResult assemblyPaymentResult(PaymentRequest request, PaymentResult result) {
        result.setOrderNos(request.getOrderNos());
        result.setCode(ResultDTOEnum.SUCCESS);
        result.setTransAmt(request.getTransAmt());
        result.setPaymentReceipt (request.getPaymentReceipt ());
        return result;
    }

    /**
     * 支付失败操作
     *
     * @param request 支付成功对象
     * @param result  支付结果对象
     */
    private void onFailure(PaymentRequest request, PaymentResult result) {
        Long paymentId = request.getPaymentId();
        int updateStatus = paymentMapper.updateStatus(paymentId, PaymentEnum.Status.FAIL.getCode(), PaymentEnum.Status.WAITING.getCode());
        if (updateStatus != 1) {
            log.error("支付单：[{}]由待支付变更成支付失败错误", paymentId);
            throw new ProviderException("支付请求失败，请稍后再试");
        }
    }

    /**
     * 支付成功操作
     *
     * @param request 支付成功对象
     * @param result  支付结果对象
     */
    protected abstract void onSuccess(PaymentRequest request, PaymentResult result);

    /**
     * 调用支付接口
     *
     * @param request 支付请求对象
     * @return 支付结果
     */
    protected abstract PaymentResult processPay(PaymentRequest request);

    /**
     * 创建支付单
     *
     * @param request 支付请求对象
     * @return 支付单id
     */
    protected Long createPaymentOrder(PaymentRequest request) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            Payment payment = doCreatePaymentOrder(request);
            transactionManager.commit(status);
            return payment.getId();
        } catch (Exception e) {
            transactionManager.rollback(status);
            log.error("支付单生成失败", e);
            throw new ProviderException("本次支付交易失败，请稍后再试", e);
        }
    }

    public Payment doCreatePaymentOrder(PaymentRequest request) {
        // 新建支付单
        String paymentNo = Global.generatePaymentNo();
        request.setPaymentNo(paymentNo);
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        Payment payment = new Payment();
        Long tenantId = loginContextInfoDTO.getTenantId();
        payment.setTenantId(tenantId);
        payment.setStoreId(loginContextInfoDTO.getStoreId());
        payment.setAccountId(loginContextInfoDTO.getAccountId());
        payment.setSpAppid(request.getSpAppid());
        payment.setSpMchid(request.getSpMchid());
        payment.setStatus(PaymentEnum.Status.WAITING.getCode());
        payment.setPaymentNo(paymentNo);
        payment.setOnlinePayChannel(request.getOnlinePayChannel());
        payment.setTradeType(request.getTradeType());
        List<PaymentItem> paymentItemList = request.getPaymentItemList();
        payment.setTotalPrice(request.getTransAmt());
        MerchantStoreAccountVO merchantStoreAccountVO = merchantStoreAccountService.queryAccountInfo(loginContextInfoDTO.getAccountId(), loginContextInfoDTO.getTenantId());
        payment.setSpOpenid(request.getH5Request() ? merchantStoreAccountVO.getOaOpenId() : merchantStoreAccountVO.getOpenId());
        paymentMapper.insertSelective(payment);
        paymentItemList.forEach(el -> {
            el.setTenantId(tenantId);
            el.setPaymentId(payment.getId());
        });
        paymentItemMapper.batchInsert(paymentItemList);
        return payment;
    }

    /**
     * 预处理 主要做参数校验
     *
     * @param paymentRequest 支付请求对象
     */
    protected void preProcess(PaymentRequest paymentRequest) {
        if (CollectionUtils.isEmpty(paymentRequest.getOrderNos())) {
            throw new ParamsException("订单编号不能为空");
        }
        Set<String> orderNos = new HashSet<>(paymentRequest.getOrderNos());
        List<OrderResp> orders = RpcResultUtil.handle(orderQueryProvider.queryByNos(paymentRequest.getOrderNos()));
        List<OrderResp> orderList = orders.stream()
                .filter(e -> OrderEnums.OrderType.COMBINE.getCode().equals(e.getOrderType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderList)) {
            Set<Long> combineOrderIds = orderList.stream().map(OrderResp::getCombineOrderId).collect(Collectors.toSet());
            // 根据组合订单Id查询所有商品
            List<OrderResp> combineOrders = RpcResultUtil.handle(combineOrderQueryProvider.queryByCombineIds(combineOrderIds, UserLoginContextUtil.getRequestContextInfoDTO().getTenantId()));
            List<String> orderNoList = combineOrders.stream().map(OrderResp::getOrderNo).collect(Collectors.toList());
            orderNos.addAll(orderNoList);
            orders = RpcResultUtil.handle(orderQueryProvider.queryByNos(new ArrayList<>(orderNos)));
        }
        if (!orders.stream().allMatch(e -> OrderStatusEnum.NO_PAYMENT.getCode().equals(e.getStatus()))) {
            throw new BizException("您的订单暂时无法支付，请稍后尝试");
        }
        // 组装一些信息
        paymentRequest.setOrders(orders);

        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        if(loginContextInfoDTO == null){
            loginContextInfoDTO = new LoginContextInfoDTO();
            loginContextInfoDTO.setTenantId(orders.get(0).getTenantId());
            loginContextInfoDTO.setStoreId(orders.get(0).getStoreId());
            loginContextInfoDTO.setAccountId(orders.get(0).getAccountId());
            ThreadTokenHolder.setToken(loginContextInfoDTO);
        }
    }

    /**
     * 组装支付的信息
     *
     * @param paymentRequest 支付请求对象
     */
    protected void assemblyPaymentInfo(PaymentRequest paymentRequest) {
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        Long tenantId = loginContextInfoDTO.getTenantId();
        // 组装支付信息
        List<OrderResp> orders = paymentRequest.getOrders();
        List<PaymentItem> paymentItems = orders.stream().map(order -> {
            PaymentItem item = new PaymentItem();
            item.setOrderId(order.getId());
            item.setOrderPrice(order.getPayablePrice());
            item.setPaymentReceipt (paymentRequest.getPaymentReceipt ());
            return item;
        }).collect(Collectors.toList());
        // 这里组合金额已经在前置流程分配好了，如果不是组合支付，则金额取订单金额
        BigDecimal transAmt = paymentRequest.isCombineRequest() ? paymentRequest.getTransAmt() : paymentItems.stream().map(PaymentItem::getOrderPrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        paymentRequest.setTenantId(tenantId);
        paymentRequest.setPaymentItemList(paymentItems);
        paymentRequest.setTransAmt(transAmt);
        assemblyPaymentDesc(paymentRequest);
    }

    /**
     * 组装支付单的描述
     *
     * @param request 支付请求对象
     */
    private void assemblyPaymentDesc(PaymentRequest request) {
        List<OrderResp> orders = request.getOrders();
        Set<Long> orderIds = orders.stream().map(OrderResp::getId).collect(Collectors.toSet());
        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotService.selectByOrderIds(request.getTenantId(), orderIds);

        String finalDesc = orderItemSnapshots.stream().map(OrderItemSnapshot::getTitle).collect(Collectors.joining("，"));
        String endingDesc = String.format("等，共%d个商品", orderItemSnapshots.size());
        finalDesc = com.cosfo.mall.common.utils.StringUtils.trimToLengthUTF8(finalDesc, endingDesc, DESC_SIZE_LIMIT);
        request.setPaymentDesc(finalDesc);
    }

    /**
     * 加锁
     */
    private RLock lock() {
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        String redisKey = RedisKeyEnum.C00005.join(loginContextInfoDTO.getStoreId());
        RLock lock = redissonClient.getLock(redisKey);
        try {
            // 等待超时3秒 未获取到锁，退出
            if (!lock.tryLock(3, TimeUnit.SECONDS)) {
                throw new BizException("该订单正在被其他人支付，请稍后重试");
            }
        } catch (InterruptedException e) {
            log.error("redis锁中断异常, key={}", redisKey, e);
            throw new BizException("该订单正在被其他人支付，请稍后重试");
        }
        return lock;
    }


    /**
     * 校验上次支付单状态
     * 1、没有支付单 则没有发起支付请求过 流程结束
     * 2、有成功的支付单 则告知用户已成功支付
     * 3、有处理中的支付单 则取消对应的支付单
     * 4、有失败的是支付单 流程结束
     *
     * @param request
     * @return
     */
    protected void verifyLastPaymentStatus(PaymentRequest request) {
        Long orderId = request.getPaymentItemList().get(0).getOrderId();
        PaymentItem lastPaymentItem = paymentItemMapper.selectByOrderId(request.getTenantId(), orderId);
        if (lastPaymentItem == null) {
            return;
        }

        Payment lastPayment = paymentMapper.selectByPrimaryKey(lastPaymentItem.getPaymentId());
        if (Objects.isNull(lastPayment)) {
            return;
        }

        String tradeType = request.getTradeType();
        String lastTradeType = lastPayment.getTradeType();
        if (!Objects.equals(tradeType, lastTradeType)) {
            log.info("本次：[{}]和上次：[{}]支付交易类型不一致，处理上次支付单结果流程结束", tradeType, lastTradeType);
            return;
        }

        request.setPaymentId(lastPayment.getId());
        PaymentResult result = queryLastPaymentResult(request);
        if (result == null) {
            return;
        }
        OrderPayResultDTO orderPayResultDTO = result.getOrderPayResultDTO();
        if (Objects.equals(orderPayResultDTO.getBank_code(), HuiFuApi.TRADE_NOT_FOUND_BANK_CODE)) {
            log.error("订单id：[{}]，支付单id：[{}]，交易不存在，结束关单", orderId, lastPayment.getId());
            return;
        }
        if (Objects.equals(PaymentEnum.Status.SUCCESS.getCode(), orderPayResultDTO.getPaymentStatus())) {
            // 支付成功操作
            paySuccess(request, result);
            // 告知用户已经支付成功
            throw new BizException("已成功支付，请勿重复支付");
        }
        if (Objects.equals(PaymentEnum.Status.DEALING.getCode(), orderPayResultDTO.getPaymentStatus())) {
            // 处理中的则取消
            closePayRequest(request, lastPayment);
        }
    }

    /**
     * 支付成功操作
     *
     * @param request
     * @param result
     */
    protected abstract void paySuccess(PaymentRequest request, PaymentResult result);

    /**
     * 关闭支付请求
     *
     * @param request
     * @param lastPayment
     */
    private void closePayRequest(PaymentRequest request, Payment lastPayment) {
        // 调用关单接口
        request.setPaymentNo(lastPayment.getPaymentNo());
        request.setPaymentCreateTime(lastPayment.getCreateTime());
        callClosePayRequest(request);

        int size = paymentMapper.updateStatus(lastPayment.getId(), PaymentEnum.Status.CANCELED.getCode(), lastPayment.getStatus());
        if (size != NumberConstant.ONE) {
            log.error("支付单号：[{}] 关闭支付请求更新状态失败", lastPayment.getPaymentNo());
            throw new BizException("当前支付操作失败，请稍后重试");
        }
    }

    /**
     * 查询上次支付单结果
     *
     * @param paymentRequest
     * @return
     */
    public abstract PaymentResult queryLastPaymentResult(PaymentRequest paymentRequest);

    /**
     * 调用关闭支付单接口
     *
     * @param paymentRequest
     * @return
     */
    public abstract boolean callClosePayRequest(PaymentRequest paymentRequest);


    /**
     * 扩展操作接口
     *
     * @param paymentRequest
     * @param paymentResult
     */
    protected void postProcess(PaymentRequest paymentRequest, PaymentResult paymentResult) {

    }
}
