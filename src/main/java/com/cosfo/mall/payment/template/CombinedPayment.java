package com.cosfo.mall.payment.template;

import cn.hutool.core.lang.Pair;
import com.cosfo.mall.common.constants.PayCodeEnum;
import com.cosfo.mall.common.constants.PayTypeEnum;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.factory.PayStrategyFactory;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.model.vo.MerchantStoreAccountVO;
import com.cosfo.mall.merchant.service.MerchantStoreAccountService;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceService;
import com.cosfo.mall.order.model.bo.OrderNonCashCalculationResultBO;
import com.cosfo.mall.payment.mapper.PaymentCombinedDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentCombinedOrderDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.tenant.service.TenantNonCashCalculationService;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 组合支付模版
 * @author: George
 * @date: 2025-04-23
 **/
@Service
@Slf4j
public class CombinedPayment extends PayTemplate {

    @Resource
    private PayStrategyFactory payStrategyFactory;
    @Resource
    private TenantNonCashCalculationService tenantNonCashCalculationService;
    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @Resource
    private PaymentCombinedDetailMapper paymentCombinedDetailMapper;
    @Resource
    private PaymentCombinedOrderDetailMapper paymentCombinedOrderDetailMapper;
    @Resource
    private PaymentMapper paymentMapper;

    @Override
    protected void preProcess(PaymentRequest request) {
        // 一些前置的校验
        super.preProcess(request);
        // 校验组合方式
        verifyCombinedPayType(request);
        // 获取组合方式
        allocateCombinedInfo(request);
    }

    @Override
    protected void assemblyPaymentInfo(PaymentRequest paymentRequest) {
        // 组装一些基础信息
        super.assemblyPaymentInfo(paymentRequest);
        List<PaymentRequest> combinePaymentRequest = paymentRequest.getCombinePaymentRequest();
        combinePaymentRequest.forEach(combinedDetail -> {
            String tradeType = combinedDetail.getTradeType();
            payStrategyFactory.getTemplateByTradeType(tradeType).assemblyPaymentInfo(combinedDetail);
        });
    }

    @Override
    protected Long createPaymentOrder(PaymentRequest request) {
        // 生成组合支付单、相对应的字段
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            Payment payment = doCreatePaymentOrder(request);
            List<PaymentRequest> combinePaymentRequest = request.getCombinePaymentRequest();
            for (PaymentRequest paymentRequest : combinePaymentRequest) {
                Long subPaymentId = doCreateSubPaymentOrder(paymentRequest, payment);
                paymentRequest.setMasterPaymentId(payment.getId());
                paymentRequest.setPaymentId(subPaymentId);
            }
            transactionManager.commit(status);
            return payment.getId();
        } catch (Exception e) {
            transactionManager.rollback(status);
            log.error("支付单生成失败", e);
            throw new ProviderException("本次支付交易失败，请稍后再试", e);
        }
    }

    private Long doCreateSubPaymentOrder(PaymentRequest request, Payment payment) {
        // 新建支付单
        String paymentNo = payment.getPaymentNo();
        request.setPaymentNo(paymentNo);
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        PaymentCombinedDetail detail = new PaymentCombinedDetail();
        Long tenantId = loginContextInfoDTO.getTenantId();
        detail.setTenantId(tenantId);
        detail.setMasterPaymentId(payment.getId());
        detail.setMasterPaymentNo(paymentNo);
        detail.setStoreId(loginContextInfoDTO.getStoreId());
        detail.setAccountId(loginContextInfoDTO.getAccountId());
        detail.setSpAppid(request.getSpAppid());
        detail.setSpMchid(request.getSpMchid());
        detail.setPaymentNo(paymentNo);
        detail.setStatus(PaymentEnum.Status.WAITING.getCode());
        detail.setOnlinePayChannel(request.getOnlinePayChannel());
        detail.setTradeType(request.getTradeType());
        detail.setTotalPrice(request.getTransAmt());
        MerchantStoreAccountVO merchantStoreAccountVO = merchantStoreAccountService.queryAccountInfo(loginContextInfoDTO.getAccountId(), loginContextInfoDTO.getTenantId());
        detail.setSpOpenid(request.getH5Request() ? merchantStoreAccountVO.getOaOpenId() : merchantStoreAccountVO.getOpenId());
        paymentCombinedDetailMapper.insertSelective(detail);

        List<OrderResp> orders = request.getOrders();
        List<PaymentCombinedOrderDetail> collect = orders.stream().map(
                order -> {
                    PaymentCombinedOrderDetail orderDetail = new PaymentCombinedOrderDetail();
                    orderDetail.setTenantId(tenantId);
                    orderDetail.setCombinedDetailId(detail.getId());
                    orderDetail.setOrderId(order.getId());
                    orderDetail.setOrderNo(order.getOrderNo());
                    orderDetail.setTotalPrice(order.getPayablePrice());
                    return orderDetail;
                }
        ).collect(Collectors.toList());
        paymentCombinedOrderDetailMapper.batchInsert(collect);
        return detail.getId();
    }

    @Override
    protected PaymentResult processPay(PaymentRequest request) {
        List<PaymentRequest> combinePaymentRequest = request.getCombinePaymentRequest();
        PaymentResult result = new PaymentResult();
        for (PaymentRequest paymentRequest : combinePaymentRequest) {
            String tradeType = paymentRequest.getTradeType();
            result = payStrategyFactory.getTemplateByTradeType(tradeType).processPay(paymentRequest);
            if (!result.isSuccess()) {
                throw new ProviderException("本次支付交易失败，请稍后再试");
            }
        }
        return result;
    }

    @Override
    protected void onSuccess(PaymentRequest request, PaymentResult result) {
        // 更新组合支付单为处理中
        Long paymentId = request.getPaymentId();
        // 乐观更新支付单为锁定状态
        int updateStatus = paymentMapper.updateStatus(paymentId, PaymentEnum.Status.DEALING.getCode(), PaymentEnum.Status.WAITING.getCode());
        if (updateStatus != 1) {
            log.error("支付单：[{}]由待支付变更成处理中失败", paymentId);
            throw new ProviderException("本次支付交易失败，请稍后再试");
        }

        // 处理支付成功的逻辑
        List<PaymentRequest> combinePaymentRequest = request.getCombinePaymentRequest();
        for (PaymentRequest paymentRequest : combinePaymentRequest) {
            String tradeType = paymentRequest.getTradeType();
            payStrategyFactory.getTemplateByTradeType(tradeType).onSuccess(paymentRequest, result);
        }
    }

    @Override
    protected void paySuccess(PaymentRequest request, PaymentResult result) {

    }

    @Override
    public PaymentResult queryLastPaymentResult(PaymentRequest paymentRequest) {
        return null;
    }

    @Override
    public boolean callClosePayRequest(PaymentRequest paymentRequest) {
        return false;
    }


    /**
     * 校验组合方式
     * 目前支持余额/非现金 + 微信/支付宝
     *
     * @param request
     */
    private void verifyCombinedPayType(PaymentRequest request) {
        // 前面已经校验过 是合法的两个payType
        List<Integer> payTypes = request.getPayTypes();
        Integer firstPayType = payTypes.get(0);
        Integer secondPayType = payTypes.get(1);

        // 余额/非现金 + 微信/支付宝
        boolean balanceOrNonCash = Objects.equals(firstPayType, PayTypeEnum.BALANCE.getType()) || Objects.equals(firstPayType, PayTypeEnum.NON_CASH_PAY.getType());
        boolean currentPay = PayTypeEnum.isCurrentPay(secondPayType);
        if (balanceOrNonCash && currentPay) {
            return;
        }
        throw new ParamsException("组合支付类型错误");
    }

    /**
     * 分配组合信息
     *
     * @param request
     */
    private void allocateCombinedInfo(PaymentRequest request) {
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        List<Integer> payTypes = request.getPayTypes();
        Integer balancePayType = payTypes.get(0);
        Integer settlePayType = payTypes.get(1);
        BigDecimal totalPrice = calculateTotalPrice(request.getOrders());

        BigDecimal usableBalance = BigDecimal.ZERO;
        if (Objects.equals(balancePayType, PayTypeEnum.BALANCE.getType())) {
            MerchantStoreBalance merchantStoreBalance = merchantStoreBalanceService.queryCashAccountByStoreId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId());
            // 如果余额就可以覆盖了，则报错
            if (merchantStoreBalance.getBalance().compareTo(totalPrice) >= 0) {
                throw new BizException("暂不支持该种组合支付，请重试");
            }
            if (merchantStoreBalance.getBalance().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BizException("账户金额为0，无法组合支付");
            }
            usableBalance = merchantStoreBalance.getBalance();
        }
        if (Objects.equals(balancePayType, PayTypeEnum.NON_CASH_PAY.getType())) {
            List<MerchantStoreBalance> merchantStoreBalances = merchantStoreBalanceService.queryNonCashAccountByStoreId(loginContextInfoDTO.getTenantId(), loginContextInfoDTO.getStoreId());
            BigDecimal nonCashBalance = merchantStoreBalances.stream().map(MerchantStoreBalance::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            OrderNonCashCalculationResultBO calculatedResult = tenantNonCashCalculationService.calculateNonCashAmount(request.getOrderNos());
            BigDecimal usableNonCashBalance = calculatedResult.getUsableNonCashBalance();
            boolean hasUnusableItems = calculatedResult.isHasUnusableItems();
            // 没有不可支付的项 余额还可以覆盖，则报错
            if (nonCashBalance.compareTo(totalPrice) >= 0 && !hasUnusableItems) {
                throw new BizException("暂不支持该种组合支付，请重试");
            }
            if (nonCashBalance.compareTo(BigDecimal.ZERO) <= 0) {
                throw new BizException("账户金额为0，无法组合支付");
            }
            if (nonCashBalance.compareTo(usableNonCashBalance) >= 0) {
                usableBalance = usableNonCashBalance;
            } else {
                usableBalance = nonCashBalance;
            }
        }

        // 将订单金额进行分摊
        BigDecimal remainingBalance = usableBalance;
        List<OrderResp> balanceOrders = new ArrayList<>();
        List<OrderResp> settleOrders = new ArrayList<>();
        List<OrderResp> orders = request.getOrders();
        // 这里让三方仓订单优先分摊余额或者非现金之类的 方便处理预付
        orders.sort(Comparator.comparingInt(order -> Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode()) ? 0 : 1));
        for (OrderResp order : orders) {
            BigDecimal orderPrice = order.getPayablePrice();
            if (remainingBalance.compareTo(BigDecimal.ZERO) > 0) {
                if (remainingBalance.compareTo(orderPrice) >= 0) {
                    // 余额足够支付整个订单
                    balanceOrders.add(buildOrderResp(order, orderPrice));
                    remainingBalance = remainingBalance.subtract(orderPrice);
                } else {
                    // 余额只能支付部分，剩余用现结
                    balanceOrders.add(buildOrderResp(order, remainingBalance));
                    settleOrders.add(buildOrderResp(order, orderPrice.subtract(remainingBalance)));
                    remainingBalance = BigDecimal.ZERO;
                }
            } else {
                // 余额用尽，全部用现结
                settleOrders.add(buildOrderResp(order, orderPrice));
            }
        }

        List<PaymentRequest> combinedRequests = new ArrayList<>(2);

        // 第一个支付方式（余额或非现金）
        Pair<String, String> balancePayCodePair = PayCodeEnum.getPayCode(balancePayType, null, request.getH5Request(), null);
        combinedRequests.add(buildCombinedRequest(
                request,
                balanceOrders,
                balancePayType,
                usableBalance,
                balancePayCodePair.getValue(),
                null,
                null));

        // 第二个支付方式（如微信/支付宝）
        Pair<String, String> currentPayCodePair = PayCodeEnum.getPayCode(settlePayType, request.getOnlinePayChannel(), request.getH5Request(), request.getPayMark());
        BigDecimal currentAmount = totalPrice.subtract(usableBalance);

        combinedRequests.add(buildCombinedRequest(
                request,
                settleOrders,
                settlePayType,
                currentAmount,
                currentPayCodePair.getValue(),
                request.getOnlinePayChannel(),
                request.getPayMark()));
        log.info("组合支付信息：{}", combinedRequests);
        request.setCombinePaymentRequest(combinedRequests);
    }

    private OrderResp buildOrderResp(OrderResp order, BigDecimal totalPrice) {
        OrderResp resp = new OrderResp();
        BeanUtils.copyProperties(order, resp);
        resp.setPayablePrice(totalPrice);
        return resp;
    }

    private BigDecimal calculateTotalPrice(List<OrderResp> orders) {
        return orders != null ? orders.stream()
                .map(OrderResp::getPayablePrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                : BigDecimal.ZERO;
    }

    private PaymentRequest buildCombinedRequest(PaymentRequest request, List<OrderResp> orders, Integer payType, BigDecimal amount, String tradeType, Integer onlinePayChannel, Integer payMark) {
        return PaymentRequest.builder()
                .tradeType(tradeType)
                .transAmt(amount)
                .payType(payType)
                .payMark(payMark)
                .onlinePayChannel(onlinePayChannel)
                .orderNos(request.getOrderNos())
                .H5Request(request.getH5Request())
                .paymentReceipt(request.getPaymentReceipt())
                .orders(orders)
                .orderNos(orders.stream().map(OrderResp::getOrderNo).collect(Collectors.toList()))
                .combineRequest(true)
                .build();
    }


    @Override
    protected PaymentResult assemblyPaymentResult(PaymentRequest request, PaymentResult result) {
        // 组装响应结果给前端
        super.assemblyPaymentResult(request, result);
        List<PaymentRequest> combinePaymentRequest = request.getCombinePaymentRequest();
        for (PaymentRequest combinedDetail : combinePaymentRequest) {
            String tradeType = combinedDetail.getTradeType();
            result = payStrategyFactory.getTemplateByTradeType(tradeType).assemblyPaymentResult(combinedDetail, result);
        }
        return result;
    }
}
