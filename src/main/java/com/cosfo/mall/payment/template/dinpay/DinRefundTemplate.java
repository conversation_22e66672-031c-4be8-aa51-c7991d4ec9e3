package com.cosfo.mall.payment.template.dinpay;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.context.OutRefundResultEnum;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.SpringContextUtil;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.facade.dto.payment.PaymentChannelQueryByIdDTO;
import com.cosfo.mall.facade.payment.PaymentChannelFacade;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.model.request.RefundRequest;
import com.cosfo.mall.payment.model.result.RefundExecuteResult;
import com.cosfo.mall.payment.service.RefundAcctSplitDetailService;
import com.cosfo.mall.payment.template.RefundTemplate;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.routing.common.enums.PaymentDictionaryEnums;
import net.summerfarm.payment.trade.adapter.dinpay.DinPayConfig;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.PaymentAttachInfoDTO;
import net.summerfarm.payment.trade.common.enums.RefundStatus;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.request.UnifiedQueryRefundRequest;
import net.summerfarm.payment.trade.model.request.UnifiedRefundRequest;
import net.summerfarm.payment.trade.model.response.UnifiedQueryRefundResult;
import net.summerfarm.payment.trade.model.response.UnifiedRefundResult;
import net.summerfarm.payment.trade.service.PaymentClientService;
import net.xianmu.common.exception.ProviderException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 智付退款模板
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Slf4j
@Service
public abstract class DinRefundTemplate extends RefundTemplate {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RefundAcctSplitDetailService refundAcctSplitDetailService;
    @Resource
    private PaymentClientService paymentClientService;
    @Value("${notify-domain}")
    private String notifyDomain;
    @Resource
    private PaymentChannelFacade paymentChannelFacade;

    /**
     * 获取支付配置
     * @param orgPaymentChannelId 原交易渠道
     * @return
     */
    protected DinPayConfig getDinPayConfig(Long orgPaymentChannelId) {
        PaymentChannelQueryByIdDTO paymentChannelQueryByIdDTO = paymentChannelFacade.queryPaymentChannelById(orgPaymentChannelId);
        if (paymentChannelQueryByIdDTO == null) {
            throw new ProviderException("未查询到原支付渠道");
        }
        DinPayConfig config = new DinPayConfig();
        config.setMerchantNo(paymentChannelQueryByIdDTO.getMerchantNo());
        config.setPublicKey(paymentChannelQueryByIdDTO.getPublicKey());
        config.setPrivateKey(paymentChannelQueryByIdDTO.getPrivateKey());
        config.setSecret(paymentChannelQueryByIdDTO.getSecret());
        return config;
    }

    /**
     * 创建退款单
     */
    @Override
    protected Long createRefundOrder(RefundRequest request) {
        PaymentItem item = paymentItemMapper.selectPaySuccessByOrderId(request.getTenantId(), request.getOrderId());
        Payment payment = paymentMapper.selectByPrimaryKey(item.getPaymentId());

        Refund refund = new Refund();
        String refundNo = Global.generateRefundNo();
        refund.setTenantId(request.getTenantId());
        refund.setAfterSaleId(request.getOrderAfterSaleId());
        refund.setSubMchid(payment.getSpMchid());
        refund.setRefundNo(refundNo);
        refund.setPaymentPrice(payment.getTotalPrice());
        refund.setCreateTime(LocalDateTime.now());
        refund.setRefundStatus(!CollectionUtils.isEmpty(request.getRefundAcctSplitDetailDTOList()) ?
                RefundEnum.Status.CONFIRM_REFUND.getStatus() : RefundEnum.Status.CREATE_REFUND.getStatus());
        refund.setRefundPrice(request.getRefundPrice());
        refund.setPaymentId(payment.getId());
        refundMapper.insertSelective(refund);

        if (!CollectionUtils.isEmpty(request.getRefundAcctSplitDetailDTOList())) {
            // 插入退款分账明细
            refundAcctSplitDetailService.save(request.getRefundAcctSplitDetailDTOList(), refund.getId());
        }
        return refund.getId();
    }

    /**
     * 查询上次退款结果
     */
    @Override
    protected OutRefundResultEnum doLastRefundResult(RefundExecuteRequest request) {
        try {
            Refund refund = request.getRefund();
            Payment payment = request.getPayment();

            log.info("开始查询智付退款结果 - 退款单ID：{}，退款单号：{}",
                    refund.getId(), refund.getRefundNo());

            // 之前没尝试退款过
            if (refund.getRetryNum() != null && refund.getRetryNum() > 0) {
                return OutRefundResultEnum.FAIL;
            }

            // 构建统一退款查询请求
            UnifiedQueryRefundRequest queryRequest = buildUnifiedQueryRefundRequest(refund, payment);

            // 调用统一退款查询服务
            UnifiedQueryRefundResult queryResult = paymentClientService.queryRefund(queryRequest);

            // 处理查询结果
            return handleQueryRefundResult(queryResult, refund);

        } catch (Exception e) {
            log.error("智付退款结果查询异常 - 退款单ID：{}，错误：{}",
                    request.getRefund().getId(), e.getMessage(), e);
            return OutRefundResultEnum.PROCESSING;
        }
    }

    /**
     * 执行退款
     */
    @Override
    protected RefundExecuteResult processRefund(RefundExecuteRequest request) {
        try {
            Refund refund = request.getRefund();
            Payment payment = request.getPayment();

            log.info("开始智付退款处理 - 退款单ID：{}，退款单号：{}，退款金额：{}",
                    refund.getId(), refund.getRefundNo(), refund.getRefundPrice());

            // 构建统一退款请求
            UnifiedRefundRequest refundRequest = buildUnifiedRefundRequest(refund, payment);

            // 调用统一退款服务
            UnifiedRefundResult refundResult = paymentClientService.refund(refundRequest);

            // 处理退款结果
            return handleRefundResult(refundResult, refund);

        } catch (Exception e) {
            log.error("智付退款处理异常 - 退款单ID：{}，错误：{}",
                    request.getRefund().getId(), e.getMessage(), e);
            throw new ProviderException("智付退款处理失败：" + e.getMessage());
        }
    }

    /**
     * 构建统一退款请求
     */
    private UnifiedRefundRequest buildUnifiedRefundRequest(Refund refund, Payment payment) {
        // 获取智付配置
        Long paymentChannelId = payment.getPaymentChannelId();
        DinPayConfig dinPayConfig = getDinPayConfig(paymentChannelId);
        ChannelConfig channelConfig = buildChannelConfig(dinPayConfig);

        return UnifiedRefundRequest.builder()
                .paymentNo(payment.getPaymentNo())
                .refundNo(refund.getRefundNo())
                .refundAmount(refund.getRefundPrice().multiply(new BigDecimal("100")).intValue())
                .notifyUrl(notifyDomain + "/pay-notify/din-refund")
                .tenantId(refund.getTenantId())
                .channelConfig(channelConfig)
                .proEnv(SpringContextUtil.isPro())
                .build();
    }

    /**
     * 构建统一退款查询请求
     */
    private UnifiedQueryRefundRequest buildUnifiedQueryRefundRequest(Refund refund, Payment payment) {
        // 获取智付配置
        DinPayConfig dinPayConfig = getDinPayConfig(payment.getPaymentChannelId());
        ChannelConfig channelConfig = buildChannelConfig(dinPayConfig);

        return UnifiedQueryRefundRequest.builder()
                .refundNo(refund.getRefundNo())
                .tenantId(refund.getTenantId())
                .channelConfig(channelConfig)
                .proEnv(SpringContextUtil.isPro())
                .build();
    }

    /**
     * 构建渠道配置
     */
    private ChannelConfig buildChannelConfig(DinPayConfig dinPayConfig) {
        ChannelConfig channelConfig = new ChannelConfig();
        channelConfig.setMerchantNo(dinPayConfig.getMerchantNo());
        channelConfig.setSecretKey(dinPayConfig.getSecret());
        channelConfig.setPublicKey(dinPayConfig.getPublicKey());
        channelConfig.setPrivateKey(dinPayConfig.getPrivateKey());
        channelConfig.setChannelCode(String.valueOf(PaymentChannelProviderEnums.DIN_PAY.getCode()));
        channelConfig.setChannelName(PaymentChannelProviderEnums.DIN_PAY.getChannelName());
        return channelConfig;
    }

    /**
     * 处理退款结果
     */
    private RefundExecuteResult handleRefundResult(UnifiedRefundResult refundResult, Refund refund) {
        if (refundResult == null) {
            log.error("智付退款结果为空 - 退款单ID：{}", refund.getId());
            throw new ProviderException("智付退款结果为空");
        }

        log.info("智付退款结果 - 退款单ID：{}，状态：{}，渠道退款ID：{}",
                refund.getId(), refundResult.getStatus(), refundResult.getChannelRefundId());

        // 根据退款状态判断是否成功
        if (RefundStatus.SUCCESS.equals(refundResult.getStatus()) ||
                RefundStatus.PENDING.equals(refundResult.getStatus())) {
            return RefundExecuteResult.builder().isSuccess(true).build();
        } else {
            return RefundExecuteResult.builder().isSuccess(false).build();
        }
    }

    /**
     * 处理退款查询结果
     */
    private OutRefundResultEnum handleQueryRefundResult(UnifiedQueryRefundResult queryResult, Refund refund) {
        if (queryResult == null) {
            log.warn("智付退款查询结果为空 - 退款单ID：{}", refund.getId());
            return OutRefundResultEnum.PROCESSING;
        }

        RefundStatus status = queryResult.getStatus();
        log.info("智付退款查询结果 - 退款单ID：{}，状态：{}", refund.getId(), status);

        if (RefundStatus.SUCCESS.equals(status)) {
            return OutRefundResultEnum.SUCCESS;
        } else if (RefundStatus.FAILED.equals(status) || RefundStatus.CLOSED.equals(status)) {
            return OutRefundResultEnum.FAIL;
        } else {
            return OutRefundResultEnum.PROCESSING;
        }
    }

    @Override
    protected void onFailure(RefundExecuteRequest request, RefundExecuteResult result) {
        log.info("智付退款失败处理 - 退款单ID：{}", request.getRefund().getId());
    }

    @Override
    protected void onSuccess(RefundExecuteRequest request, RefundExecuteResult result) {
        log.info("智付退款成功处理 - 退款单ID：{}", request.getRefund().getId());
    }
}
