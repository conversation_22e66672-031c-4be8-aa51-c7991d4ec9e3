package com.cosfo.mall.payment.utils;

import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.cosfo.mall.bill.service.BillProfitSharingRefundService;
import com.cosfo.mall.common.context.OrderItemFeeEnum;
import com.cosfo.mall.order.model.bo.OrderItemFeeBO;
import com.cosfo.mall.order.model.po.OrderItemFeeTransaction;
import com.cosfo.mall.order.service.OrderItemFeeCalculateService;
import com.cosfo.mall.order.service.OrderItemFeeTransactionService;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.service.RefundAcctSplitDetailService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-24
 * @Description:
 */
@Slf4j
@Component
public class RefundComponent {

    @Resource
    private OrderItemFeeCalculateService orderItemFeeCalculateService;

    @Resource
    private OrderItemFeeTransactionService orderItemFeeTransactionService;

    @Resource
    private RefundMapper payStrategyRefundMapper;

    @Resource
    private PaymentMapper paymentMapper;

    @Resource
    private TransactionTemplate payStrategyTransactionTemplate;
    @Resource
    private RefundAcctSplitDetailService refundAcctSplitDetailService;
    @Lazy
    @Resource
    private BillProfitSharingRefundService billProfitSharingRefundService;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;

    /**
     * 发起HTTP调用前事务
     *
     * @param refundDTO
     * @param payment
     * @param orderAfterSale
     * @return 本地事务是否执行、退款信息
     */
//    public Pair<Boolean, Refund> preRefundExecute(RefundDTO refundDTO, Payment payment, OrderAfterSaleResp orderAfterSale) {
//        // 前置本地事务
//        AtomicReference<Refund> refundReference = new AtomicReference<>();
//        boolean executeResult = payStrategyTransactionTemplate.execute(status -> {
//            Boolean result = true;
//            try {
//                // 锁一定存在的数据,来保证幂等
//                final Payment finalPayment = paymentMapper.selectByPrimaryKeyForUpdate(payment.getId());
//                Refund existRefund = payStrategyRefundMapper.selectByAfterSaleId(refundDTO.getTenantId(), refundDTO.getAfterSaleId());
//                if (Objects.nonNull(existRefund)) {
//                    throw new BizException("该售后单号退款信息已存在", ErrorCodeEnum.EXACTLY_ONCE_CREATE_REFUND);
//                }
//                // 退款计算
//                RefundCalculateResultBO refundCalculateResultBO = billProfitSharingRefundService.doRefundSharing(orderAfterSale.getId());
//                if (refundCalculateResultBO != null) {
//                    refundDTO.setRefundAcctSplitDetailDTOList(refundCalculateResultBO.getRefundAcctSplitDetailDTOS());
//                }
//
//                // 创建退款信息
//                Refund refund = createRefundInfo(finalPayment, refundDTO);
//                refundReference.set(refund);
//                // 生成退款的订单明细交易信息
//                generateOrderItemFee(orderAfterSale);
//            } catch (BizException e) {
//                if (ErrorCodeEnum.EXACTLY_ONCE_CREATE_REFUND == e.getErrorCode()) {
//                    log.info("幂等控制,创建退款信息拦截,该售后单号退款信息已存在,refundDTO:{}", JSON.toJSONString(refundDTO));
//                } else {
//                    log.warn("BizException 创建退款信息失败,refundDTO:{},e", JSON.toJSONString(refundDTO), e);
//                }
//                status.setRollbackOnly();
//                result = false;
//            } catch (Exception e) {
//                log.error("DefaultServiceException：创建退款信息失败,refundDTO:{},{}", JSON.toJSONString(refundDTO), e.getMessage(), e);
//                status.setRollbackOnly();
//                result = false;
//            }
//            return result;
//        });
//
//        return Pair.of(executeResult, refundReference.get());
//    }

    /**
     * 创建退款信息
     * @param payment
     * @param refundDTO
     * @return 是否应该忽略，数据
     */
//    private Refund createRefundInfo(Payment payment, RefundDTO refundDTO) {
//        String refundNo = Global.generateRefundNo();
//        Integer status = RefundEnum.Status.CREATE_REFUND.getStatus();
//        List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOList = refundDTO.getRefundAcctSplitDetailDTOList();
//        if(!CollectionUtils.isEmpty(refundAcctSplitDetailDTOList)){
//            status = RefundEnum.Status.CONFIRM_REFUND.getStatus();
//        }
//
//        Refund refund = new Refund();
//        BeanUtil.copyProperties(refundDTO, refund);
//        refund.setSubMchid(payment.getSpMchid());
//        refund.setRefundNo(refundNo);
//        refund.setRefundStatus(status);
//        refund.setPaymentPrice(payment.getTotalPrice());
//        refund.setCreateTime(LocalDateTime.now());
//        payStrategyRefundMapper.insertSelective(refund);
//
//        // 保存逆向分账明细快照
//        if (!CollectionUtils.isEmpty(refundAcctSplitDetailDTOList)) {
//            refundAcctSplitDetailService.save(refundAcctSplitDetailDTOList, refund.getId());
//        }
//
//        return refund;
//    }

    public void generateOrderItemFee(Long orderAfterSaleId) {
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(orderAfterSaleId)));
        OrderAfterSaleResp orderAfterSaleDTO = afterSaleDTOList.get(0);
        generateOrderItemFee(orderAfterSaleDTO);
    }

    /**
     * 生成退款的订单明细交易信息
     */
    public void generateOrderItemFee(OrderAfterSaleResp orderAfterSale) {
        OrderItemFeeBO orderItemFeeBO = orderItemFeeCalculateService.buildOrderItemRefundBO(orderAfterSale.getId());
        if (orderItemFeeBO.getFee().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        OrderItemFeeTransaction orderItemFeeTransaction = new OrderItemFeeTransaction();
        orderItemFeeTransaction.setTenantId(orderAfterSale.getTenantId());
        orderItemFeeTransaction.setOrderId(orderAfterSale.getOrderId());
        orderItemFeeTransaction.setOrderItemId(orderAfterSale.getOrderItemId());
        orderItemFeeTransaction.setOrderAfterSaleId(orderAfterSale.getId());
        orderItemFeeTransaction.setFee(orderItemFeeBO.getFee().negate());
        orderItemFeeTransaction.setFeeType(OrderItemFeeEnum.FeeType.AGENT_FEE.getCode());
        orderItemFeeTransaction.setTransactionType(OrderItemFeeEnum.TransactionType.REFUND.getCode());
        orderItemFeeTransactionService.saveOrderItemFeeTransaction(Arrays.asList(orderItemFeeTransaction));
    }
}
