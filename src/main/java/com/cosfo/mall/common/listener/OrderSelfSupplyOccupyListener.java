package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.common.constant.MqTagConstant;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.stock.model.dto.OrderSelfSupplyOccupyDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/20
 */
@Slf4j
@Component
@MqListener(topic = MQTopicConstant.SELF_SUPPLY_ORDER_OCCUPY,
        consumerGroup = MqGroupConstant.GID_SELF_SUPPLY_ORDER_OCCUPY,
        tag = MqTagConstant.TAG_SELF_SUPPLY_ORDER_OCCUPY
)
public class OrderSelfSupplyOccupyListener extends AbstractMqListener<OrderSelfSupplyOccupyDTO> {
    @Resource
    private OrderService orderService;

    @Override
    public void process(OrderSelfSupplyOccupyDTO orderSelfSupplyOccupyDTO) {
        log.info("rocketmq 收到自营仓下单消息，消息内容：{}", JSONObject.toJSONString(orderSelfSupplyOccupyDTO));
        orderService.orderOccupyBySpecifyWarehouseAndSku(orderSelfSupplyOccupyDTO);
    }
}
