package com.cosfo.mall.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/1/11
 */
@Getter
@AllArgsConstructor
public enum AliPaySceneEnum {

    /**
     * 详见：https://paas.huifu.com/open/doc/api/#/api_ggcsbm?id=%e5%be%ae%e4%bf%a1%e6%94%af%e4%bb%98%e5%9c%ba%e6%99%af
     */
    ONLINE_SCAN("1", "线上扫码");

    /**
     * 状态类型编码
     */
    private String code;
    /**
     * 状态类型描述
     */
    private String desc;
}
