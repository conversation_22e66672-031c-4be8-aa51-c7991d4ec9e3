package com.cosfo.mall.common.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Component
@Slf4j
public class ProfitSharingResultTask extends XianMuJavaProcessorV2 {
    @Resource
    private BillProfitSharingService billProfitSharingService;

    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("分账结果查询任务开始------");

        billProfitSharingService.queryProfitSharingResult();
        log.info("分账结果查询任务结束------");
        return new ProcessResult(true);
    }
}
