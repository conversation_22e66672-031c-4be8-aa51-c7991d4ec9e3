package com.cosfo.mall.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date : 2022/12/21 14:48
 */
@Configuration
@Data
public class HuiFuConfig {
    /**
     * 系统号
     */
    @Value("${huifu.sys_id}")
    private String sysId;
    /**
     * 产品号
     */
    @Value("${huifu.product_id}")
    private String productId;
    /**
     * 渠道号
     */
    @Value("${huifu.channel_no}")
    private String channelNo;
    /**
     * 客思服汇付私钥
     */
    @Value("${huifu.private_key}")
    private String privateKey;
}
