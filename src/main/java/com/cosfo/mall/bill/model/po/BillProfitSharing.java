package com.cosfo.mall.bill.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * bill_profit_sharing
 * <AUTHOR>
@Data
public class BillProfitSharing implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 接收方租户id
     */
    private Long receiverTenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * appId
     */
    private String appId;

    /**
     * 账号类型
     */
    private String type;

    /**
     * 付款方账号
     */
    private String account;

    /**
     * 交易流水号
     */
    private String transactionId;

    /**
     * 外部交易订单号
     */
    private String outTradeNo;

    /**
     * 交易金额
     */
    private BigDecimal price;

    /**
     * 0、待分账 1、成功 2、失败 3、取消 4、处理中
     */
    private Integer status;

    /**
     * 成功时间
     */
    private Date successTime;

    /**
     * 分账描述
     */
    private String description;

    /**
     * 微信分账单号
     */
    private String wxOrderId;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 分账明细单号
     */
    private String detailId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
    /**
     * 汇付商号
     */
    private String huifuId;
    /**
     * 业务类型 1 正向分账 2，逆向分账退款
     */
    private Integer businessType;

    /**
     * 售后单Id
     */
    private Long afterSaleId;

    /**
     * 账号类型
     *
     * @see com.cosfo.mall.common.context.shard.AccountTypeEnum
     */
    private Integer accountType;

    /**
     * 分账渠道 1、汇付 2、智付
     *
     * @see com.cosfo.mall.common.constants.OnlinePayChannelEnum
     */
    private Integer profitSharingChannel;

    private static final long serialVersionUID = 1L;
}