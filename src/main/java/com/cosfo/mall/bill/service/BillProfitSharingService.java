package com.cosfo.mall.bill.service;

import com.cosfo.mall.bill.model.dto.BillProfitSharingDTO;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharing;
import com.cosfo.mall.order.model.dto.HuiFuConfirmRefundResultDTO;
import com.cosfo.mall.payment.model.dto.RefundDTO;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
public interface BillProfitSharingService {

    /**
     * 查询分账结果
     */
    void queryProfitSharingResult();

    /**
     * 查询分账结果（带时间参数）
     *
     * @param startTime 开始时间，格式：yyyy-MM-dd HH:mm:ss
     * @param endTime 结束时间，格式：yyyy-MM-dd HH:mm:ss
     */
    void queryProfitSharingResult(String startTime, String endTime);

    /**
     * 更新分账结果
     *
     * @param billProfitSharing
     */
    void updateByPrimaryKeySelective(BillProfitSharing billProfitSharing);

    /**
     * 批量更新
     * @param update
     * @param sharingIds
     */
    void batchUpdateBillProFitSharing(BillProfitSharing update, List<Long> sharingIds);

    /**
     * 查询订单分装规则快照
     *
     * @return
     */
    List<BillProfitSharingSnapshotDTO> queryOrderProfitSharingRuleSnapshot(Long tenantId, Long orderId);

    /**
     * 更新分账明细
     *
     * @param billProfitSharingSnapshotDTO
     */
    void updateBillProfitSharingSnapshot(BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO);

    /**
     * 保存分账单
     *
     * @param billProfitSharing
     */
    void saveBillProfitSharing(BillProfitSharing billProfitSharing);

    /**
     * 创建交易确认退款记录
     *
     * @param refundDTO
     * @return
     */
    List<BillProfitSharingDTO> createConfirmRefundRecord(RefundDTO refundDTO);

    /**
     * 处理交易确认退款结果
     *
     * @param huiFuConfirmRefundResultDTO
     * @param billProfitSharingDTOList
     */
    boolean handleConfirmRefundRecordResult(HuiFuConfirmRefundResultDTO huiFuConfirmRefundResultDTO, List<BillProfitSharingDTO> billProfitSharingDTOList, Long tenantId);

    /**
     * 查询交易确认退款记录
     *
     * @param tenantId
     * @param afterSaleId
     * @return
     */
    List<BillProfitSharingDTO> queryConfirmResultRecord(Long tenantId, String confirmRefundReqId);

    /**
     * 根据分账单号查询
     *
     * @param tenantId
     * @param profitSharingNo
     * @return
     */
    List<BillProfitSharingSnapshotDTO> queryByProfitSharingNo(Long tenantId, String profitSharingNo);

    /**
     * 批量保存分账单
     *
     * @param billProfitSharingList
     */
    void batchSaveBillProfitSharing(List<BillProfitSharing> billProfitSharingList);
}
