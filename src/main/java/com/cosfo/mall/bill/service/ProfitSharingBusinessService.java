package com.cosfo.mall.bill.service;

import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.mall.order.model.po.OrderAgentSkuFeeRuleSnapshot;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProfitSharingBusinessService {

    /**
     * 处理分账
     *
     * @param billProfitSharingOrderDto
     */
    void handleOrderProfitSharing(BillProfitSharingOrderDTO billProfitSharingOrderDto);

    /**
     * 计算分账
     *
     * @param billProfitSharingOrderDto
     */
    boolean calculateProfitSharing(BillProfitSharingOrderDTO billProfitSharingOrderDto);

    /**
     * 处理代仓分账
     *
     * @param orderId
     * @param tenantId
     * @return
     */
    List<OrderAgentSkuFeeRuleSnapshot> handleAgentSkuProfitSharing(Long orderId, Long tenantId);

    /**
     * 实际分账
     *
     * @param profitSharingNo
     */
    void doProfitSharing(String profitSharingNo);


    /**
     * 保存分账快照
     *
     * @param orderId
     */
    void saveOrderProfitSharingRule(Long orderId);

    /**
     * 计算-实际分账为一体
     *
     * @param orderId
     */
    void profitSharingFlow(Long orderId);

    /**
     * 计算-实际分账为一体
     *
     * @param profitSharingNo
     */
    void profitSharingFlowByNo(String profitSharingNo);

    /**
     * 更新分账快照
     *
     * @param billProfitSharingSnapshot
     */
    void updateSnapshots(BillProfitSharingSnapshot billProfitSharingSnapshot);

    /**
     * 根据订单id判断是否存在分账规则
     *
     * @param orderIds
     * @return
     */
    boolean existsByOrderIds(Long tenantId, List<Long> orderIds);

    /**
     * 生成分账快照
     *
     * @param tenantId
     * @param orderIds
     */
    void generateBillProfitSharingSnapshots(Long tenantId, List<Long> orderIds);

    /**
     * 根据订单id立马进行分账
     *
     * @param tenantId
     * @param orderIds
     */
    void profitSharingByOrderIdsRightNow(Long tenantId, List<Long> orderIds);
}
