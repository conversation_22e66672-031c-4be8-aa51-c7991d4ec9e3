package com.cosfo.mall.tenant.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/28 15:03
 */
@Data
public class TenantDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司注册省份
     */
    private String province;

    /**
     * 公司注册市
     */
    private String city;

    /**
     * 公司注册区
     */
    private String area;

    /**
     * 公司注册地址
     */
    private String address;

    /**
     * adminId
     */
    private Long adminId;

    /**
     * 分账开关
     */
    @Deprecated
    private Integer profitSharingSwitch;

    /**
     * 分账渠道 0 微信 1 汇付
     */
    @Deprecated
    private Integer onlinePayChannel;

    /**
     * 无库存商品展示规则 0-收起 1-展开 2-隐藏
     */
    private String goodsShowRule;

    /**
     * 注册功能 0:关闭 1开启
     */
    private Integer registrySwitch;

    /**
     * 货币符号1:人民币 2:RM 默认人民币
     */
    private Integer currency;

    /**
     * 是否显示语言选择 0:不显示 1:显示
     */
    private Integer showLanguageSelection;
}
