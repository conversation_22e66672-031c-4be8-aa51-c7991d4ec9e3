<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.tenant.mapper.TenantPrepaymentAccountMapper">

    <update id="decreaseAvailableAmount">
        update tenant_prepayment_account
        set available_amount = available_amount - #{changeAmount}, total_amount = total_amount - #{changeAmount}, last_change_time = now()
        where id = #{id} and available_amount >= #{changeAmount}
    </update>

    <update id="increaseAvailableAmount">
        update tenant_prepayment_account
        set available_amount = available_amount + #{changeAmount}, total_amount = total_amount + #{changeAmount}, last_change_time = now()
        where id = #{id}
    </update>
    <select id="queryTenant4SupplierPrepay"
            resultType="com.cosfo.mall.tenant.model.po.TenantPrepaymentAccount">
        select id,
               tenant_id          tenantId,
               supplier_tenant_id supplierTenantId,
               payable_target     payableTarget,
               total_amount       totalAmount,
               frozen_amount      frozenAmount,
               available_amount   availableAmount,
               last_change_time   lastChangeTime,
               create_time        createTime,
               update_time        updateTime
        from tenant_prepayment_account
        where tenant_id = #{tenantId} and supplier_tenant_id = #{supplierTenantId}
        for update
    </select>

    <select id="selectByIdForUpdate" resultType="com.cosfo.mall.tenant.model.po.TenantPrepaymentAccount">
        select id,
               tenant_id          tenantId,
               supplier_tenant_id supplierTenantId,
               payable_target     payableTarget,
               total_amount       totalAmount,
               frozen_amount      frozenAmount,
               available_amount   availableAmount,
               last_change_time   lastChangeTime,
               create_time        createTime,
               update_time        updateTime
        from tenant_prepayment_account
        where id = #{id} for update
    </select>

    <update id="freezeAvailableAmount">
        update tenant_prepayment_account
        set available_amount = available_amount - #{changeAmount}, total_amount = total_amount - #{changeAmount}, frozen_amount = frozen_amount + #{changeAmount}
        where id = #{id} and available_amount >= #{changeAmount}
    </update>

    <update id="decreaseFreezeBalance">
        update tenant_prepayment_account
        set frozen_amount = frozen_amount - #{changeAmount}
        where id = #{id} and frozen_amount >= #{changeAmount}
    </update>
</mapper>
